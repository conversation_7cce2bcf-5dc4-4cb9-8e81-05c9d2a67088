{"id": "e35cf599-120b-458e-8286-98571d23f697", "revision": 0, "last_node_id": 158, "last_link_id": 235, "nodes": [{"id": 146, "type": "RH_Captioner", "pos": [1700, 200], "size": [300, 120], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 216}], "outputs": [{"name": "STRING", "type": "STRING", "links": [217], "slot_index": 0}], "properties": {"Node name for S&R": "RH_Captioner"}, "widgets_values": ["auto", "auto"], "color": "#388E3C", "bgcolor": "#E8F5E8", "title": "🤖 RH_Captioner"}, {"id": 135, "type": "LayerUtility: ImageReel", "pos": [1000, 1100], "size": [350, 258], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "image1", "name": "image1", "type": "IMAGE", "link": null}, {"localized_name": "image2", "name": "image2", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "image3", "name": "image3", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "image4", "name": "image4", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "image1_text", "name": "image1_text", "type": "STRING", "widget": {"name": "image1_text"}, "link": null}, {"localized_name": "image2_text", "name": "image2_text", "type": "STRING", "widget": {"name": "image2_text"}, "link": null}, {"localized_name": "image3_text", "name": "image3_text", "type": "STRING", "widget": {"name": "image3_text"}, "link": null}, {"localized_name": "image4_text", "name": "image4_text", "type": "STRING", "widget": {"name": "image4_text"}, "link": null}, {"localized_name": "reel_height", "name": "reel_height", "type": "INT", "widget": {"name": "reel_height"}, "link": null}, {"localized_name": "border", "name": "border", "type": "INT", "widget": {"name": "border"}, "link": null}, {"name": "image", "type": "IMAGE", "link": 221}], "outputs": [{"localized_name": "reel", "name": "reel", "type": "<PERSON><PERSON>", "slot_index": 0, "links": [223]}], "title": "🎞️ ImageReel", "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ImageReel"}, "widgets_values": [1536, 8, 0.5, "image4", 512, 32], "color": "#C2185B", "bgcolor": "#FCE4EC"}, {"id": 145, "type": "LayerUtility: ImageReelComposit", "pos": [1400, 1100], "size": [350, 210], "flags": {}, "order": 17, "mode": 0, "inputs": [{"localized_name": "reel_1", "name": "reel_1", "type": "<PERSON><PERSON>", "link": null}, {"localized_name": "reel_2", "name": "reel_2", "shape": 7, "type": "<PERSON><PERSON>", "link": null}, {"localized_name": "reel_3", "name": "reel_3", "shape": 7, "type": "<PERSON><PERSON>", "link": null}, {"localized_name": "reel_4", "name": "reel_4", "shape": 7, "type": "<PERSON><PERSON>", "link": null}, {"localized_name": "font_file", "name": "font_file", "type": "COMBO", "widget": {"name": "font_file"}, "link": null}, {"localized_name": "font_size", "name": "font_size", "type": "INT", "widget": {"name": "font_size"}, "link": null}, {"localized_name": "border", "name": "border", "type": "INT", "widget": {"name": "border"}, "link": null}, {"localized_name": "color_theme", "name": "color_theme", "type": "COMBO", "widget": {"name": "color_theme"}, "link": null}, {"name": "image", "type": "IMAGE", "link": 223}], "outputs": [{"localized_name": "image1", "name": "image1", "type": "IMAGE", "slot_index": 0, "links": [224]}], "title": "🎨 ImageReelComposit", "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ImageReelComposit"}, "widgets_values": ["horizontal", 0.5, 32, "light"], "color": "#C2185B", "bgcolor": "#FCE4EC"}, {"id": 141, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [188.0094757080078, 417.3197326660156], "size": [280, 126], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 201}, {"localized_name": "CLIPCLIP", "name": "clip", "type": "CLIP", "link": 202}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}, {"localized_name": "CLIP强度", "name": "strength_clip", "type": "FLOAT", "widget": {"name": "strength_clip"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [206]}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [207]}], "title": "⚡ LoraLoader - FusionX", "properties": {"cnr_id": "comfy-core", "ver": "0.3.48", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["Wan2.1_T2V_14B_FusionX_LoRA.safetensors", 0.4, 0.4], "color": "#7B1FA2", "bgcolor": "#F3E5F5"}, {"id": 149, "type": "CLIPLoader", "pos": [516.0031127929688, 214.6551055908203], "size": [280, 106], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "CLIP名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [202]}], "title": "📝 CLIPLoader", "properties": {"cnr_id": "comfy-core", "ver": "0.3.48", "Node name for S&R": "CLIPLoader"}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "stable_diffusion", "default"], "color": "#F57C00", "bgcolor": "#FFF3E0"}, {"id": 143, "type": "VAELoader", "pos": [840, 223.98110961914062], "size": [280, 90], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [204, 205]}], "title": "🔧 VA<PERSON><PERSON><PERSON>", "properties": {"cnr_id": "comfy-core", "ver": "0.3.48", "Node name for S&R": "VAELoader"}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#F57C00", "bgcolor": "#FFF3E0"}, {"id": 147, "type": "UNETLoader", "pos": [190.6739959716797, 222.64881896972656], "size": [280, 90], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [201]}], "title": "🧠 UNETLoader", "properties": {"cnr_id": "comfy-core", "ver": "0.3.48", "Node name for S&R": "UNETLoader"}, "widgets_values": ["wan2.2_t2v_low_noise_14B_fp8_scaled.safetensors", "default"], "color": "#F57C00", "bgcolor": "#FFF3E0"}, {"id": 142, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [525.3291625976562, 421.3165588378906], "size": [280, 126], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 206}, {"localized_name": "CLIPCLIP", "name": "clip", "type": "CLIP", "link": 207}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}, {"localized_name": "CLIP强度", "name": "strength_clip", "type": "FLOAT", "widget": {"name": "strength_clip"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [208]}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [209]}], "title": "⚡ LoraLoader - LightX2V", "properties": {"cnr_id": "comfy-core", "ver": "0.3.48", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["lightx2v_cfg_step_distill_lora.safetensors", 0.4, 0.4], "color": "#7B1FA2", "bgcolor": "#F3E5F5"}, {"id": 156, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [850.6582641601562, 419.984375], "size": [280, 126], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 208}, {"localized_name": "CLIPCLIP", "name": "clip", "type": "CLIP", "link": 209}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}, {"localized_name": "CLIP强度", "name": "strength_clip", "type": "FLOAT", "widget": {"name": "strength_clip"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [210]}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [211, 212]}], "title": "⚡ LoraLoader - Smartphone", "properties": {"cnr_id": "comfy-core", "ver": "0.3.48", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["SmartphoneSnapshotPhotoReality.safetensors", 0.7, 0.7], "color": "#7B1FA2", "bgcolor": "#F3E5F5"}, {"id": 136, "type": "CLIPTextEncode", "pos": [181.3480224609375, 815.9874877929688], "size": [450, 180], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 211}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [218]}], "title": "✅ CLIPTextEncode - Positive", "properties": {"cnr_id": "comfy-core", "ver": "0.3.48", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a high quality, detailed photograph of a person, realistic lighting, sharp focus, professional photography, photorealistic, 8k uhd, masterpiece", [false, true]], "color": "#FFA000", "bgcolor": "#FFF8E1"}, {"id": 137, "type": "CLIPTextEncode", "pos": [696.0031127929688, 819.9843139648438], "size": [450, 180], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 212}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [219]}], "title": "❌ CLIPTextEncode - Negative", "properties": {"cnr_id": "comfy-core", "ver": "0.3.48", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["blurry, low quality, distorted, deformed, ugly, bad anatomy, worst quality, low resolution, jpeg artifacts", [false, true]], "color": "#FFA000", "bgcolor": "#FFF8E1"}, {"id": 148, "type": "K<PERSON><PERSON><PERSON>", "pos": [169.35740661621094, 1126.64599609375], "size": [400, 280], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 210}, {"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 218}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 219}, {"localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 215}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [220]}], "title": "🎲 KSampler", "properties": {"cnr_id": "comfy-core", "ver": "0.3.48", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [440335433356531, "randomize", 20, 1, "bong_tangent", "res_2s", 0.4], "color": "#D32F2F", "bgcolor": "#FFEBEE"}, {"id": 151, "type": "VAEDecode", "pos": [642.0062866210938, 1141.30126953125], "size": [280, 100], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 220}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 205}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [221, 222]}], "title": "🔓 VAEDecode", "properties": {"cnr_id": "comfy-core", "ver": "0.3.48", "Node name for S&R": "VAEDecode"}, "widgets_values": [], "color": "#D32F2F", "bgcolor": "#FFEBEE"}, {"id": 157, "type": "SaveImage", "pos": [188.00942993164062, 1529.3106689453125], "size": [350, 200], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 222}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "title": "💾 SaveImage - Result", "properties": {"cnr_id": "comfy-core", "ver": "0.3.48", "Node name for S&R": "SaveImage"}, "widgets_values": ["Wan22_Enhanced"], "color": "#689F38", "bgcolor": "#F1F8E9"}, {"id": 158, "type": "SaveImage", "pos": [613.3228759765625, 1529.310546875], "size": [350, 200], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 224}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "title": "💾 SaveImage - Comparison", "properties": {"cnr_id": "comfy-core", "ver": "0.3.48", "Node name for S&R": "SaveImage"}, "widgets_values": ["Wan22_Comparison"], "color": "#689F38", "bgcolor": "#F1F8E9"}, {"id": 140, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [1291.9395751953125, 557.0774536132812], "size": [280, 330], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 213}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "aspect_ratio", "name": "aspect_ratio", "type": "COMBO", "widget": {"name": "aspect_ratio"}, "link": null}, {"localized_name": "proportional_width", "name": "proportional_width", "type": "INT", "widget": {"name": "proportional_width"}, "link": null}, {"localized_name": "proportional_height", "name": "proportional_height", "type": "INT", "widget": {"name": "proportional_height"}, "link": null}, {"localized_name": "fit", "name": "fit", "type": "COMBO", "widget": {"name": "fit"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "round_to_multiple", "name": "round_to_multiple", "type": "COMBO", "widget": {"name": "round_to_multiple"}, "link": null}, {"localized_name": "scale_to_side", "name": "scale_to_side", "type": "COMBO", "widget": {"name": "scale_to_side"}, "link": null}, {"localized_name": "scale_to_length", "name": "scale_to_length", "type": "INT", "widget": {"name": "scale_to_length"}, "link": null}, {"localized_name": "background_color", "name": "background_color", "type": "STRING", "widget": {"name": "background_color"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [214]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": null}, {"localized_name": "original_size", "name": "original_size", "type": "BOX", "links": null}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "title": "📐 ImageScaleByAspectRatio V2", "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2"}, "widgets_values": [1536, "lanc<PERSON>s", "center", "letterbox", "lanc<PERSON>s", "8", "None", 1024, "#000000"], "color": "#1976D2", "bgcolor": "#E3F2FD"}, {"id": 144, "type": "LoadImage", "pos": [1293.551513671875, 233.8533172607422], "size": [280, 280], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [213, 216]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "title": "📷 LoadImage", "properties": {"cnr_id": "comfy-core", "ver": "0.3.48", "Node name for S&R": "LoadImage"}, "widgets_values": ["example.jpg", "image"], "color": "#1976D2", "bgcolor": "#E3F2FD"}, {"id": 152, "type": "VAEEncode", "pos": [1288.7149658203125, 932.4390869140625], "size": [280, 80], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 214}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 204}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [215]}], "title": "🔒 VAEEncode", "properties": {"cnr_id": "comfy-core", "ver": "0.3.48", "Node name for S&R": "VAEEncode"}, "widgets_values": [], "color": "#1976D2", "bgcolor": "#E3F2FD"}, {"id": 139, "type": "easy showAnything", "pos": [1700, 435.76702880859375], "size": [300, 120], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "输入任何", "name": "anything", "shape": 7, "type": "*", "link": 217}], "outputs": [{"localized_name": "输出", "name": "output", "type": "*", "links": null}], "title": "👁️ easy showAnything", "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy showAnything"}, "widgets_values": [], "color": "#388E3C", "bgcolor": "#E8F5E8"}], "links": [[201, 147, 0, 141, 0, "MODEL"], [202, 149, 0, 141, 1, "CLIP"], [204, 143, 0, 152, 1, "VAE"], [205, 143, 0, 151, 1, "VAE"], [206, 141, 0, 142, 0, "MODEL"], [207, 141, 1, 142, 1, "CLIP"], [208, 142, 0, 156, 0, "MODEL"], [209, 142, 1, 156, 1, "CLIP"], [210, 156, 0, 148, 0, "MODEL"], [211, 156, 1, 136, 0, "CLIP"], [212, 156, 1, 137, 0, "CLIP"], [213, 144, 0, 140, 0, "IMAGE"], [214, 140, 0, 152, 0, "IMAGE"], [215, 152, 0, 148, 3, "LATENT"], [216, 144, 0, 146, 0, "IMAGE"], [217, 146, 0, 139, 0, "STRING"], [218, 136, 0, 148, 1, "CONDITIONING"], [219, 137, 0, 148, 2, "CONDITIONING"], [220, 148, 0, 151, 0, "LATENT"], [221, 151, 0, 135, 10, "IMAGE"], [222, 151, 0, 157, 0, "IMAGE"], [223, 135, 0, 145, 8, "IMAGE"], [224, 145, 0, 158, 0, "IMAGE"]], "groups": [{"id": 1, "title": "🤖 Step1 - 核心模型加载", "bounding": [150, 150, 1000, 180], "color": "#FFF3E0", "font_size": 24, "flags": {}}, {"id": 2, "title": "⚡ Step2 - LoRA增强链", "bounding": [150, 350, 1000, 200], "color": "#F3E5F5", "font_size": 24, "flags": {}}, {"id": 3, "title": "📷 Step3 - 图像输入处理", "bounding": [1250, 150, 404.1144104003906, 870.8958129882812], "color": "#E3F2FD", "font_size": 24, "flags": {}}, {"id": 4, "title": "🤖 Step4 - AI图像分析", "bounding": [1650, 150, 409.0169677734375, 507.9822998046875], "color": "#E8F5E8", "font_size": 24, "flags": {}}, {"id": 5, "title": "💬 Step5 - 提示词处理", "bounding": [150, 750, 1000, 250], "color": "#FFF8E1", "font_size": 24, "flags": {}}, {"id": 6, "title": "🎲 Step6 - 核心采样生成", "bounding": [150, 1050, 800, 350], "color": "#FFEBEE", "font_size": 24, "flags": {}}, {"id": 7, "title": "🎨 Step7 - 结果对比展示", "bounding": [950, 1050, 868.652099609375, 339.9056091308594], "color": "#FCE4EC", "font_size": 24, "flags": {}}, {"id": 8, "title": "💾 Step8 - 最终输出", "bounding": [150, 1450, 850, 280], "color": "#F1F8E9", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.496258064516129, "offset": [674.1106446570534, 329.7479522613665]}}, "version": 0.4}