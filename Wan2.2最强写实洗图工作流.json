{"id": "25dfce61-3571-4c9a-a7f8-6a83951d66aa", "revision": 0, "last_node_id": 158, "last_link_id": 296, "nodes": [{"id": 135, "type": "LayerUtility: ImageReel", "pos": [1000, 1300], "size": [270, 238], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "link": 267}, {"label": "image2", "name": "image2", "shape": 7, "type": "IMAGE", "link": 268}, {"label": "image3", "name": "image3", "shape": 7, "type": "IMAGE"}, {"label": "image4", "name": "image4", "shape": 7, "type": "IMAGE"}], "outputs": [{"label": "reel", "name": "reel", "type": "<PERSON><PERSON>", "links": [278]}], "properties": {"Node name for S&R": "LayerUtility: ImageReel", "cnr_id": "comfyui_layerstyle", "ver": "3bfe8e435d167b4a5bf08716729f89802dbbaa6f", "widget_ue_connectable": {}}, "widgets_values": ["原图", "洗图", "", "image4", 1536, 8], "color": "#323", "bgcolor": "#535"}, {"id": 136, "type": "CLIPTextEncode", "pos": [1000, 900], "size": [450, 150], "flags": {"collapsed": false}, "order": 14, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 269}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [282]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走"], "color": "#232", "bgcolor": "#353"}, {"id": 137, "type": "CLIPTextEncode", "pos": [1200, 900], "size": [437.6938171386719, 146.9487762451172], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 270}, {"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 271}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [281]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": ["2010年代初用手机拍摄并上传到Facebook的快照照片, 具有动态自然光照, 以及中性白色色彩平衡和褪色效果, 描绘了一位年轻女性脸部特写, 被透过薄纱窗帘的金色晨光照亮, 从侧面拍摄.\n在紧凑的1:1特写镜头中, 仅可见女性脸庞的一半, 略微转向附近的一扇窗户, 温暖, 清晨的阳光透过白色布帘倾泻而入.光线柔和地掠过她的面容, 突显出她脸颊上细腻的桃色绒毛和嘴唇附近干燥皮肤的微弱纹理.她的眼睛在画框边缘刚刚可见, 捕捉到一丝琥珀色光芒.漂浮在空气中的杂乱发丝在逆光中发光.背景模糊不清但显然是室内--可能是卧室--失焦并染上日黄色调.她表情中的轻微眯眼可见清晨的昏沉感.镜头在她脸部最亮的部分周围形成淡淡的光晕, 光线最强的区域有轻微的色彩溢出.感觉非常亲密, 像是醒来后拍下的第一张照片, 在寂静中捕捉."], "color": "#232", "bgcolor": "#353"}, {"id": 139, "type": "easy showAnything", "pos": [1200, 700], "size": [351.6213073730469, 373.6241455078125], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "shape": 7, "type": "*", "link": 272}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [291]}], "properties": {"Node name for S&R": "easy showAnything", "cnr_id": "comfyui-easy-use", "ver": "e7320ec0c463641f702add48b87363c7a9713a1c", "widget_ue_connectable": {}}, "widgets_values": ["The image depicts a woman lounging inside a large concrete pipe. Her pose is relaxed, with her legs extended and crossed, and she leans her upper body slightly backward, supported by her hands. She is dressed in a casual yet colorful outfit featuring a yellow top, bright pink shorts with a pattern, and an open white shirt that drapes casually off her arms. Her black hair is shoulder length and neatly styled. The pipe serves as a rustic frame, leading the viewer's eye toward its circular opening, which showcases a natural outdoor scene in the distance—featuring a dirt path, foliage, and soft lighting from a diffused sky. The edges of the concrete pipe are worn, and the surrounding area is framed with stone and brick, offering a textured backdrop that contrasts with the smoothness of the pipe's interior. The ground outside the pipe is strewn with leaves, adding an earthy tone to the composition. The photograph combines elements of urban and natural aesthetics, creating a visually dynamic and serene scene."], "color": "#232", "bgcolor": "#353"}, {"id": 140, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [1200, 500], "size": [336, 330], "flags": {}, "order": 6, "mode": 0, "inputs": [{"label": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 273}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [287]}, {"label": "mask", "name": "mask", "type": "MASK"}, {"label": "original_size", "name": "original_size", "type": "BOX"}, {"label": "width", "name": "width", "type": "INT"}, {"label": "height", "name": "height", "type": "INT"}], "properties": {"Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2", "cnr_id": "comfyui_layerstyle", "ver": "3bfe8e435d167b4a5bf08716729f89802dbbaa6f", "widget_ue_connectable": {}}, "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "8", "longest", 1536, "#000000"], "color": "#223", "bgcolor": "#335"}, {"id": 141, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [1000, 300], "size": [413.3301086425781, 133.2881317138672], "flags": {}, "order": 5, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 274}, {"label": "clip", "name": "clip", "type": "CLIP", "link": 275}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [276]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [277]}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": ["Wan2.1_T2V_14B_FusionX_LoRA.safetensors", 0.4000000000000001, 0.4000000000000001], "color": "#332922", "bgcolor": "#593930"}, {"id": 142, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [1200, 300], "size": [438.8387451171875, 140.66421508789062], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 276}, {"label": "clip", "name": "clip", "type": "CLIP", "link": 277}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [293]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [294]}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": ["Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32.safetensors", 0.4000000000000001, 0.4000000000000001], "color": "#332922", "bgcolor": "#593930"}, {"id": 143, "type": "VAELoader", "pos": [1400, 100], "size": [367.7924499511719, 70.40070343017578], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [286, 288]}], "properties": {"Node name for S&R": "VAELoader", "cnr_id": "comfy-core", "ver": "0.3.44", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors?download=true", "directory": "vae"}], "widget_ue_connectable": {}}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 145, "type": "LayerUtility: ImageReelComposit", "pos": [1200, 1300], "size": [277.20001220703125, 190], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "reel_1", "name": "reel_1", "type": "<PERSON><PERSON>", "link": 278}, {"label": "reel_2", "name": "reel_2", "shape": 7, "type": "<PERSON><PERSON>"}, {"label": "reel_3", "name": "reel_3", "shape": 7, "type": "<PERSON><PERSON>"}, {"label": "reel_4", "name": "reel_4", "shape": 7, "type": "<PERSON><PERSON>"}], "outputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "links": [296]}], "properties": {"Node name for S&R": "LayerUtility: ImageReelComposit", "cnr_id": "comfyui_layerstyle", "ver": "3bfe8e435d167b4a5bf08716729f89802dbbaa6f", "widget_ue_connectable": {}}, "widgets_values": ["Alibaba-PuHuiTi-Heavy.ttf", 40, 8, "dark"], "color": "#323", "bgcolor": "#535"}, {"id": 146, "type": "RH_Captioner", "pos": [1000, 700], "size": [364.04083251953125, 196.65501403808594], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "ref_image", "name": "ref_image", "type": "IMAGE", "link": 279}], "outputs": [{"label": "describe", "name": "describe", "type": "STRING", "links": [272]}], "properties": {"Node name for S&R": "RH_Captioner", "widget_ue_connectable": {}}, "widgets_values": ["Please provide a detailed description of this image. If any characters in the image are familiar to you, such as celebrities, movie characters, or animated figures, please directly use their names. The description should be as detailed as possible, but should not exceed 200 words."], "color": "#223", "bgcolor": "#335"}, {"id": 147, "type": "UNETLoader", "pos": [1000, 100], "size": [420.1534118652344, 83.15281677246094], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [274]}], "properties": {"Node name for S&R": "UNETLoader", "cnr_id": "comfy-core", "ver": "0.3.44", "models": [{"name": "wan2.1_t2v_1.3B_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_1.3B_fp16.safetensors?download=true", "directory": "diffusion_models"}], "widget_ue_connectable": {}}, "widgets_values": ["wan2.2_t2v_low_noise_14B_fp8_scaled.safetensors", "fp8_e4m3fn"], "color": "#223", "bgcolor": "#335"}, {"id": 148, "type": "K<PERSON><PERSON><PERSON>", "pos": [1000, 1100], "size": [250, 474], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 280}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 281}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 282}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 283}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [285]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": [552131291806724, "randomize", 20, 1, "res_2s", "bong_tangent", 0.4000000000000001], "color": "#323", "bgcolor": "#535"}, {"id": 149, "type": "CLIPLoader", "pos": [1200, 100], "size": [360.6138610839844, 98], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [275]}], "properties": {"Node name for S&R": "CLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.44", "models": [{"name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true", "directory": "text_encoders"}], "widget_ue_connectable": {}}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#432", "bgcolor": "#653"}, {"id": 150, "type": "PathchSageAttentionKJ", "pos": [800, 1100], "size": [244.58575439453125, 62.97879409790039], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 284}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [289]}], "properties": {"Node name for S&R": "PathchSageAttentionKJ", "widget_ue_connectable": {}}, "widgets_values": ["auto"], "color": "#223", "bgcolor": "#335"}, {"id": 151, "type": "VAEDecode", "pos": [1200, 1100], "size": [246.62803649902344, 59.8025016784668], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 285}, {"label": "vae", "name": "vae", "type": "VAE", "link": 286}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [268, 295]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#323", "bgcolor": "#535"}, {"id": 152, "type": "VAEEncode", "pos": [1400, 500], "size": [241.07766723632812, 46], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "pixels", "name": "pixels", "type": "IMAGE", "link": 287}, {"label": "vae", "name": "vae", "type": "VAE", "link": 288}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [283]}], "properties": {"Node name for S&R": "VAEEncode", "cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#323", "bgcolor": "#535"}, {"id": 153, "type": "ModelSamplingSD3", "pos": [800, 900], "size": [237.0276641845703, 60.845035552978516], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 289}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [280]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "cnr_id": "comfy-core", "ver": "0.3.45", "widget_ue_connectable": {}}, "widgets_values": [4.000000000000001], "color": "#223", "bgcolor": "#335"}, {"id": 154, "type": "CR Text Concatenate", "pos": [800, 700], "size": [343.8714904785156, 126], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "text1", "name": "text1", "shape": 7, "type": "STRING", "link": 290}, {"label": "text2", "name": "text2", "shape": 7, "type": "STRING", "link": 291}], "outputs": [{"label": "STRING", "name": "STRING", "type": "*", "links": [271]}, {"label": "show_help", "name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Text Concatenate", "cnr_id": "comfyroll", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "widget_ue_connectable": {}}, "widgets_values": ["，", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 155, "type": "CFGZeroStarAndInit", "pos": [800, 1300], "size": [270, 82], "flags": {}, "order": 13, "mode": 4, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 292}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [284]}], "properties": {"Node name for S&R": "CFGZeroStarAndInit", "cnr_id": "comfyui-kjnodes", "ver": "d9425173e77b5be8c75492a72424ddffae4d4445", "widget_ue_connectable": {}}, "widgets_values": [true, 0], "color": "#323", "bgcolor": "#535"}, {"id": 156, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [1400, 300], "size": [434.2865295410156, 126], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 293}, {"label": "clip", "name": "clip", "type": "CLIP", "link": 294}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [292]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [269, 270]}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": ["WAN2.1_SmartphoneSnapshotPhotoReality_v1_by-AI_Characters.safetensors", 0.7000000000000002, 0.7000000000000002], "color": "#332922", "bgcolor": "#593930"}, {"id": 157, "type": "SaveImage", "pos": [1000, 1500], "size": [547.776611328125, 891.9474487304688], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 295}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI", ""], "color": "#232", "bgcolor": "#353"}, {"id": 158, "type": "SaveImage", "pos": [1200, 1500], "size": [1128.177001953125, 873.9366455078125], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 296}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI", ""], "color": "#232", "bgcolor": "#353"}, {"id": 138, "type": "CR Text", "pos": [800, 500], "size": [327.2252502441406, 113], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "text", "name": "text", "type": "*", "links": [290]}, {"label": "show_help", "name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Text", "cnr_id": "comfyroll", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "widget_ue_connectable": {}}, "widgets_values": ["2010 年代初期用手机拍摄并上传到 Facebook 的快照照片，具有动态自然光和中性白平衡和褪色颜色，"], "color": "#232", "bgcolor": "#353"}, {"id": 144, "type": "LoadImage", "pos": [1000, 500], "size": [556.7559814453125, 898.8511352539062], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [267, 273, 279]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}}, "widgets_values": ["0b112015b8c0e07fd7bdb7befc5014666c12d9f328ded9228740b2c231a0d1e0.webp", "image", ""], "color": "#232", "bgcolor": "#353"}], "links": [[267, 144, 0, 135, 0, "IMAGE"], [268, 151, 0, 135, 1, "IMAGE"], [269, 156, 1, 136, 0, "CLIP"], [270, 156, 1, 137, 0, "CLIP"], [271, 154, 0, 137, 1, "STRING"], [272, 146, 0, 139, 0, "*"], [273, 144, 0, 140, 0, "IMAGE"], [274, 147, 0, 141, 0, "MODEL"], [275, 149, 0, 141, 1, "CLIP"], [276, 141, 0, 142, 0, "MODEL"], [277, 141, 1, 142, 1, "CLIP"], [278, 135, 0, 145, 0, "<PERSON><PERSON>"], [279, 144, 0, 146, 0, "IMAGE"], [280, 153, 0, 148, 0, "MODEL"], [281, 137, 0, 148, 1, "CONDITIONING"], [282, 136, 0, 148, 2, "CONDITIONING"], [283, 152, 0, 148, 3, "LATENT"], [284, 155, 0, 150, 0, "MODEL"], [285, 148, 0, 151, 0, "LATENT"], [286, 143, 0, 151, 1, "VAE"], [287, 140, 0, 152, 0, "IMAGE"], [288, 143, 0, 152, 1, "VAE"], [289, 150, 0, 153, 0, "MODEL"], [290, 138, 0, 154, 0, "STRING"], [291, 139, 0, 154, 1, "STRING"], [292, 156, 0, 155, 0, "MODEL"], [293, 142, 0, 156, 0, "MODEL"], [294, 142, 1, 156, 1, "CLIP"], [295, 151, 0, 157, 0, "IMAGE"], [296, 145, 0, 158, 0, "IMAGE"]], "groups": [{"id": 5, "title": "B站、YouTube、公众号：嘟嘟AI绘画趣味学", "bounding": [5534.97900390625, -116.9299545288086, 2016.12744140625, 132.8913116455078], "color": "#b06634", "font_size": 100, "flags": {}}, {"id": 6, "title": "模型", "bounding": [5760.982421875, 100.6403579711914, 970, 654], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "采样", "bounding": [6761.046875, 94.0856704711914, 1345.80615234375, 592.1827392578125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "尺寸提示词处理", "bounding": [4652.52978515625, 94.3405532836914, 1086.057861328125, 664.5821533203125], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.49500000000000116, "offset": [-4566.8371602857005, 298.0668805223106]}, "VHS_KeepIntermediate": true, "links_added_by_ue": [], "workspace_info": {"id": "2DPvFpHLLMzmEMupNJKw3"}, "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "frontendVersion": "1.23.4", "VHS_latentpreview": false}, "version": 0.4}