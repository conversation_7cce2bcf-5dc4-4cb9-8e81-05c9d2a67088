{"id": "wan22-beautiful-layout", "revision": 0, "last_node_id": 158, "last_link_id": 235, "nodes": [{"id": 147, "type": "UNETLoader", "pos": [200, 200], "size": [280, 90], "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [201], "slot_index": 0}], "title": "🧠 Wan2.2 14B Model", "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["wan2.2_t2v_low_noise_14B_fp8_scaled.safetensors", "default"], "color": "#2E3440", "bgcolor": "#3B4252"}, {"id": 149, "type": "CLIPLoader", "pos": [520, 200], "size": [280, 90], "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "CLIP", "type": "CLIP", "links": [202, 203], "slot_index": 0}], "title": "📝 CLIP Text Encoder", "properties": {"Node name for S&R": "CLIPLoader"}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors"], "color": "#2E3440", "bgcolor": "#3B4252"}, {"id": 143, "type": "VAELoader", "pos": [840, 200], "size": [280, 90], "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "VAE", "type": "VAE", "links": [204, 205], "slot_index": 0}], "title": "🔧 VAE Encoder/Decoder", "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#2E3440", "bgcolor": "#3B4252"}, {"id": 141, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [200, 380], "size": [280, 120], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 201}, {"name": "clip", "type": "CLIP", "link": 202}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [206], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [207], "slot_index": 1}], "title": "⚡ FusionX LoRA", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["Wan2.1_T2V_14B_FusionX_LoRA.safetensors", 0.4, 0.4], "color": "#5E81AC", "bgcolor": "#81A1C1"}, {"id": 142, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [520, 380], "size": [280, 120], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 206}, {"name": "clip", "type": "CLIP", "link": 207}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [208], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [209], "slot_index": 1}], "title": "⚡ LightX2V LoRA", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["lightx2v_cfg_step_distill_lora.safetensors", 0.4, 0.4], "color": "#5E81AC", "bgcolor": "#81A1C1"}, {"id": 156, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [840, 380], "size": [280, 120], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 208}, {"name": "clip", "type": "CLIP", "link": 209}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [210], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [211, 212], "slot_index": 1}], "title": "⚡ Smartphone Reality", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["SmartphoneSnapshotPhotoReality.safetensors", 0.7, 0.7], "color": "#5E81AC", "bgcolor": "#81A1C1"}, {"id": 144, "type": "LoadImage", "pos": [1300, 200], "size": [320, 280], "flags": {}, "order": 6, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [213, 216], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "title": "📷 Load Input Image", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.jpg", "image"], "color": "#88C0D0", "bgcolor": "#8FBCBB"}, {"id": 140, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [1300, 520], "size": [320, 100], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 213}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [214], "slot_index": 0}], "title": "📐 Smart Resize (1536px)", "properties": {"Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2"}, "widgets_values": [1536, "lanc<PERSON>s", "center"], "color": "#88C0D0", "bgcolor": "#8FBCBB"}, {"id": 152, "type": "VAEEncode", "pos": [1300, 660], "size": [320, 80], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 214}, {"name": "vae", "type": "VAE", "link": 204}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [215], "slot_index": 0}], "title": "🔒 VAE Encode", "properties": {"Node name for S&R": "VAEEncode"}, "widgets_values": [], "color": "#88C0D0", "bgcolor": "#8FBCBB"}, {"id": 146, "type": "RH_Captioner", "pos": [1700, 200], "size": [350, 120], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 216}], "outputs": [{"name": "STRING", "type": "STRING", "links": [217], "slot_index": 0}], "title": "🤖 AI Image Captioner", "properties": {"Node name for S&R": "RH_Captioner"}, "widgets_values": ["auto", "auto"], "color": "#A3BE8C", "bgcolor": "#B48EAD"}, {"id": 139, "type": "easy showAnything", "pos": [1700, 360], "size": [350, 120], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "anything", "type": "*", "link": 217}], "outputs": [], "title": "👁️ Caption Display", "properties": {"Node name for S&R": "easy showAnything"}, "widgets_values": [], "color": "#A3BE8C", "bgcolor": "#B48EAD"}, {"id": 136, "type": "CLIPTextEncode", "pos": [200, 800], "size": [450, 180], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 211}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [218], "slot_index": 0}], "title": "✅ Positive Prompt", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a high quality, detailed photograph of a person, realistic lighting, sharp focus, professional photography, photorealistic, 8k uhd, masterpiece"], "color": "#EBCB8B", "bgcolor": "#D08770"}, {"id": 137, "type": "CLIPTextEncode", "pos": [700, 800], "size": [450, 180], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 212}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [219], "slot_index": 0}], "title": "❌ Negative Prompt", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["blurry, low quality, distorted, deformed, ugly, bad anatomy, worst quality, low resolution, jpeg artifacts"], "color": "#BF616A", "bgcolor": "#D08770"}, {"id": 148, "type": "K<PERSON><PERSON><PERSON>", "pos": [200, 1050], "size": [400, 280], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 210}, {"name": "positive", "type": "CONDITIONING", "link": 218}, {"name": "negative", "type": "CONDITIONING", "link": 219}, {"name": "latent_image", "type": "LATENT", "link": 215}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [220], "slot_index": 0}], "title": "🎲 High-Quality Sampler", "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [440335433356531, "randomize", 20, 1.0, "bong_tangent", "res_2s", 0.4], "color": "#D08770", "bgcolor": "#EBCB8B"}, {"id": 151, "type": "VAEDecode", "pos": [650, 1050], "size": [280, 100], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 220}, {"name": "vae", "type": "VAE", "link": 205}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [221, 222], "slot_index": 0}], "title": "🔓 VAE Decode", "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": [], "color": "#D08770", "bgcolor": "#EBCB8B"}, {"id": 135, "type": "LayerUtility: ImageReel", "pos": [1000, 1050], "size": [350, 140], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 221}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [223], "slot_index": 0}], "title": "🎞️ Image Reel (8 Frames)", "properties": {"Node name for S&R": "LayerUtility: ImageReel"}, "widgets_values": [1536, 8, 0.5], "color": "#B48EAD", "bgcolor": "#8FBCBB"}, {"id": 145, "type": "LayerUtility: ImageReelComposit", "pos": [1400, 1050], "size": [350, 140], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 223}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [224], "slot_index": 0}], "title": "🎨 Comparison Composite", "properties": {"Node name for S&R": "LayerUtility: ImageReelComposit"}, "widgets_values": ["horizontal", 0.5], "color": "#B48EAD", "bgcolor": "#8FBCBB"}, {"id": 157, "type": "SaveImage", "pos": [200, 1400], "size": [350, 200], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 222}], "outputs": [], "title": "💾 Save Enhanced Result", "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["Wan22_Enhanced"], "color": "#4C566A", "bgcolor": "#3B4252"}, {"id": 158, "type": "SaveImage", "pos": [600, 1400], "size": [350, 200], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 224}], "outputs": [], "title": "💾 Save Comparison", "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["Wan22_Comparison"], "color": "#4C566A", "bgcolor": "#3B4252"}], "links": [[201, 147, 0, 141, 0, "MODEL"], [202, 149, 0, 141, 1, "CLIP"], [203, 149, 0, 137, 0, "CLIP"], [204, 143, 0, 152, 1, "VAE"], [205, 143, 0, 151, 1, "VAE"], [206, 141, 0, 142, 0, "MODEL"], [207, 141, 1, 142, 1, "CLIP"], [208, 142, 0, 156, 0, "MODEL"], [209, 142, 1, 156, 1, "CLIP"], [210, 156, 0, 148, 0, "MODEL"], [211, 156, 1, 136, 0, "CLIP"], [212, 156, 1, 137, 0, "CLIP"], [213, 144, 0, 140, 0, "IMAGE"], [214, 140, 0, 152, 0, "IMAGE"], [215, 152, 0, 148, 3, "LATENT"], [216, 144, 0, 146, 0, "IMAGE"], [217, 146, 0, 139, 0, "STRING"], [218, 136, 0, 148, 1, "CONDITIONING"], [219, 137, 0, 148, 2, "CONDITIONING"], [220, 148, 0, 151, 0, "LATENT"], [221, 151, 0, 135, 0, "IMAGE"], [222, 151, 0, 157, 0, "IMAGE"], [223, 135, 0, 145, 0, "IMAGE"], [224, 145, 0, 158, 0, "IMAGE"]], "groups": [{"title": "🤖 Core Models - Wan2.2 14B Foundation", "bounding": [150, 150, 1000, 180], "color": "#4C566A", "font_size": 24, "flags": {}}, {"title": "⚡ LoRA Enhancement Chain - Triple Power Boost", "bounding": [150, 330, 1000, 200], "color": "#5E81AC", "font_size": 24, "flags": {}}, {"title": "📷 Image Input & Processing Pipeline", "bounding": [1250, 150, 420, 620], "color": "#88C0D0", "font_size": 24, "flags": {}}, {"title": "🤖 AI Analysis & Caption Generation", "bounding": [1650, 150, 450, 350], "color": "#A3BE8C", "font_size": 24, "flags": {}}, {"title": "💬 Prompt Engineering Studio", "bounding": [150, 750, 1000, 250], "color": "#EBCB8B", "font_size": 24, "flags": {}}, {"title": "🎲 High-Performance Sampling Engine", "bounding": [150, 1000, 780, 350], "color": "#D08770", "font_size": 24, "flags": {}}, {"title": "🎨 Visual Comparison & Effects", "bounding": [950, 1000, 850, 220], "color": "#B48EAD", "font_size": 24, "flags": {}}, {"title": "💾 Professional Output Management", "bounding": [150, 1350, 850, 280], "color": "#4C566A", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.8, "offset": [0, 0]}}, "version": 0.4}