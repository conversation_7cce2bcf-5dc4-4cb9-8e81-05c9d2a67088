{"last_node_id": 158, "last_link_id": 235, "nodes": [{"id": 147, "type": "UNETLoader", "pos": [1000, 100], "size": [315, 82], "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [201], "slot_index": 0}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["wan2.2_t2v_low_noise_14B_fp8_scaled.safetensors", "default"], "color": "#223", "bgcolor": "#335"}, {"id": 149, "type": "CLIPLoader", "pos": [1200, 100], "size": [315, 82], "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "CLIP", "type": "CLIP", "links": [202, 203], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPLoader"}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors"], "color": "#223", "bgcolor": "#335"}, {"id": 143, "type": "VAELoader", "pos": [1400, 100], "size": [315, 58], "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "VAE", "type": "VAE", "links": [204, 205], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#223", "bgcolor": "#335"}, {"id": 141, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [1000, 300], "size": [315, 126], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 201}, {"name": "clip", "type": "CLIP", "link": 202}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [206], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [207], "slot_index": 1}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["Wan2.1_T2V_14B_FusionX_LoRA.safetensors", 0.4, 0.4], "color": "#432", "bgcolor": "#653"}, {"id": 142, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [1200, 300], "size": [315, 126], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 206}, {"name": "clip", "type": "CLIP", "link": 207}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [208], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [209], "slot_index": 1}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["lightx2v_cfg_step_distill_lora.safetensors", 0.4, 0.4], "color": "#432", "bgcolor": "#653"}, {"id": 156, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [1400, 300], "size": [315, 126], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 208}, {"name": "clip", "type": "CLIP", "link": 209}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [210], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [211, 212], "slot_index": 1}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["SmartphoneSnapshotPhotoReality.safetensors", 0.7, 0.7], "color": "#432", "bgcolor": "#653"}, {"id": 144, "type": "LoadImage", "pos": [1000, 500], "size": [315, 314], "flags": {}, "order": 6, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [213], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.jpg", "image"], "color": "#223", "bgcolor": "#335"}, {"id": 140, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [1200, 500], "size": [315, 130], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 213}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [214], "slot_index": 0}], "properties": {"Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2"}, "widgets_values": [1536, "lanc<PERSON>s", "center"], "color": "#223", "bgcolor": "#335"}, {"id": 152, "type": "VAEEncode", "pos": [1400, 500], "size": [210, 46], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 214}, {"name": "vae", "type": "VAE", "link": 204}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [215], "slot_index": 0}], "properties": {"Node name for S&R": "VAEEncode"}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 146, "type": "RH_Captioner", "pos": [1000, 700], "size": [315, 106], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 213}], "outputs": [{"name": "STRING", "type": "STRING", "links": [216], "slot_index": 0}], "properties": {"Node name for S&R": "RH_Captioner"}, "widgets_values": ["auto", "auto"], "color": "#223", "bgcolor": "#335"}, {"id": 139, "type": "easy showAnything", "pos": [1200, 700], "size": [315, 106], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "anything", "type": "*", "link": 216}], "outputs": [], "properties": {"Node name for S&R": "easy showAnything"}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 136, "type": "CLIPTextEncode", "pos": [1000, 900], "size": [400, 200], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 211}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [217], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a high quality, detailed photograph of a person, realistic lighting, sharp focus, professional photography"], "color": "#232", "bgcolor": "#353"}, {"id": 137, "type": "CLIPTextEncode", "pos": [1200, 900], "size": [400, 200], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 212}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [218], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["blurry, low quality, distorted, deformed, ugly, bad anatomy"], "color": "#322", "bgcolor": "#533"}, {"id": 148, "type": "K<PERSON><PERSON><PERSON>", "pos": [1000, 1100], "size": [315, 262], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 210}, {"name": "positive", "type": "CONDITIONING", "link": 217}, {"name": "negative", "type": "CONDITIONING", "link": 218}, {"name": "latent_image", "type": "LATENT", "link": 215}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [219], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [440335433356531, "randomize", 20, 1.0, "bong_tangent", "res_2s", 0.4], "color": "#323", "bgcolor": "#535"}, {"id": 151, "type": "VAEDecode", "pos": [1200, 1100], "size": [210, 46], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 219}, {"name": "vae", "type": "VAE", "link": 205}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [220, 221], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": [], "color": "#323", "bgcolor": "#535"}, {"id": 135, "type": "LayerUtility: ImageReel", "pos": [1000, 1300], "size": [315, 150], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 220}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [222], "slot_index": 0}], "properties": {"Node name for S&R": "LayerUtility: ImageReel"}, "widgets_values": [1536, 8, 0.5], "color": "#432", "bgcolor": "#653"}, {"id": 145, "type": "LayerUtility: ImageReelComposit", "pos": [1200, 1300], "size": [315, 150], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 222}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [223], "slot_index": 0}], "properties": {"Node name for S&R": "LayerUtility: ImageReelComposit"}, "widgets_values": ["horizontal", 0.5], "color": "#432", "bgcolor": "#653"}, {"id": 157, "type": "SaveImage", "pos": [1000, 1500], "size": [315, 270], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 221}], "outputs": [], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["Wan22_result"], "color": "#222", "bgcolor": "#000"}, {"id": 158, "type": "SaveImage", "pos": [1200, 1500], "size": [315, 270], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 223}], "outputs": [], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["Wan22_comparison"], "color": "#222", "bgcolor": "#000"}], "links": [[201, 147, 0, 141, 0, "MODEL"], [202, 149, 0, 141, 1, "CLIP"], [203, 149, 0, 137, 0, "CLIP"], [204, 143, 0, 152, 1, "VAE"], [205, 143, 0, 151, 1, "VAE"], [206, 141, 0, 142, 0, "MODEL"], [207, 141, 1, 142, 1, "CLIP"], [208, 142, 0, 156, 0, "MODEL"], [209, 142, 1, 156, 1, "CLIP"], [210, 156, 0, 148, 0, "MODEL"], [211, 156, 1, 136, 0, "CLIP"], [212, 156, 1, 137, 0, "CLIP"], [213, 144, 0, 140, 0, "IMAGE"], [214, 140, 0, 152, 0, "IMAGE"], [215, 152, 0, 148, 3, "LATENT"], [216, 146, 0, 139, 0, "STRING"], [217, 136, 0, 148, 1, "CONDITIONING"], [218, 137, 0, 148, 2, "CONDITIONING"], [219, 148, 0, 151, 0, "LATENT"], [220, 151, 0, 135, 0, "IMAGE"], [221, 151, 0, 157, 0, "IMAGE"], [222, 135, 0, 145, 0, "IMAGE"], [223, 145, 0, 158, 0, "IMAGE"]], "groups": [{"title": "🤖 Step1 - 核心模型加载", "bounding": [950, 50, 550, 200], "color": "#FFF3E0", "font_size": 24}, {"title": "⚡ Step2 - LoRA增强链", "bounding": [950, 250, 550, 200], "color": "#F3E5F5", "font_size": 24}, {"title": "📷 Step3 - 图像输入处理", "bounding": [950, 450, 550, 200], "color": "#E3F2FD", "font_size": 24}, {"title": "🤖 Step4 - AI图像分析", "bounding": [950, 650, 550, 200], "color": "#E8F5E8", "font_size": 24}, {"title": "💬 Step5 - 提示词处理", "bounding": [950, 850, 650, 200], "color": "#FFF8E1", "font_size": 24}, {"title": "🎲 Step6 - 核心采样生成", "bounding": [950, 1050, 550, 200], "color": "#FFEBEE", "font_size": 24}, {"title": "🎨 Step7 - 结果对比展示", "bounding": [950, 1250, 550, 200], "color": "#FCE4EC", "font_size": 24}, {"title": "💾 Step8 - 最终输出", "bounding": [950, 1450, 550, 350], "color": "#F1F8E9", "font_size": 24}], "config": {}, "extra": {}, "version": 0.4}